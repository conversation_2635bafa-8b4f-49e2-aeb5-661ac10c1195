using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Services.Implementations
{
    public class TimesheetApiService : ITimesheetApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl;

        public TimesheetApiService(HttpClient httpClient, IAuthenticationService authService, ApiConfiguration apiConfig)
        {
            _httpClient = httpClient;
            _authService = authService;
            _baseUrl = apiConfig.BaseUrl;
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                return true;
            }
            return false;
        }

        public async Task<List<Timesheet>> GetTimesheetsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<Timesheet>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/timesheets");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var timesheets = JsonSerializer.Deserialize<List<Timesheet>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return timesheets ?? new List<Timesheet>();
                }

                return new List<Timesheet>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting timesheets: {ex.Message}");
                return new List<Timesheet>();
            }
        }

        public async Task<List<Timesheet>> GetTimesheetsByTutorAsync(int tutorId)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<Timesheet>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/timesheets/tutor/{tutorId}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var timesheets = JsonSerializer.Deserialize<List<Timesheet>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return timesheets ?? new List<Timesheet>();
                }

                return new List<Timesheet>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting timesheets for tutor {tutorId}: {ex.Message}");
                return new List<Timesheet>();
            }
        }

        public async Task<Timesheet?> GetTimesheetAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/timesheets/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Timesheet>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting timesheet {id}: {ex.Message}");
                return null;
            }
        }

        public async Task<Timesheet?> CreateTimesheetAsync(Timesheet timesheet)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(timesheet);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/timesheets", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Timesheet>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating timesheet: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateTimesheetAsync(int id, Timesheet timesheet)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(timesheet);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"{_baseUrl}/timesheets/{id}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating timesheet {id}: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteTimesheetAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/timesheets/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting timesheet {id}: {ex.Message}");
                return false;
            }
        }

        // TimesheetEntry operations
        public async Task<List<TimesheetEntry>> GetTimesheetEntriesAsync(int timesheetId)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<TimesheetEntry>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/timesheets/{timesheetId}/entries");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var entries = JsonSerializer.Deserialize<List<TimesheetEntry>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return entries ?? new List<TimesheetEntry>();
                }

                return new List<TimesheetEntry>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting timesheet entries for timesheet {timesheetId}: {ex.Message}");
                return new List<TimesheetEntry>();
            }
        }

        public async Task<TimesheetEntry?> GetTimesheetEntryAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/timesheets/entries/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<TimesheetEntry>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting timesheet entry {id}: {ex.Message}");
                return null;
            }
        }

        public async Task<TimesheetEntry?> CreateTimesheetEntryAsync(TimesheetEntry entry)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(entry);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/timesheets/entries", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<TimesheetEntry>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating timesheet entry: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateTimesheetEntryAsync(int id, TimesheetEntry entry)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(entry);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"{_baseUrl}/timesheets/entries/{id}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating timesheet entry {id}: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteTimesheetEntryAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/timesheets/entries/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting timesheet entry {id}: {ex.Message}");
                return false;
            }
        }
    }
}
