using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface ITimesheetApiService
    {
        Task<List<Timesheet>> GetTimesheetsAsync();
        Task<List<Timesheet>> GetTimesheetsByTutorAsync(int tutorId);
        Task<Timesheet?> GetTimesheetAsync(int id);
        Task<Timesheet?> CreateTimesheetAsync(Timesheet timesheet);
        Task<bool> UpdateTimesheetAsync(int id, Timesheet timesheet);
        Task<bool> DeleteTimesheetAsync(int id);
        
        // TimesheetEntry operations
        Task<List<TimesheetEntry>> GetTimesheetEntriesAsync(int timesheetId);
        Task<TimesheetEntry?> GetTimesheetEntryAsync(int id);
        Task<TimesheetEntry?> CreateTimesheetEntryAsync(TimesheetEntry entry);
        Task<bool> UpdateTimesheetEntryAsync(int id, TimesheetEntry entry);
        Task<bool> DeleteTimesheetEntryAsync(int id);
    }
}
