using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class PaymentReminderService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PaymentReminderService> _logger;
        private readonly IConfiguration _configuration;
        private readonly TimeSpan _period;
        private readonly int _lessonThreshold;
        private readonly int _paymentDeadlineDays;

        public PaymentReminderService(
            IServiceProvider serviceProvider,
            ILogger<PaymentReminderService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;

            // Read configuration settings - check environment variables first, then fall back to appsettings.json
            var reminderIntervalHours = GetConfigurationValue("PAYMENT_REMINDER_INTERVAL_HOURS", "PaymentReminder:IntervalHours", 24);
            _lessonThreshold = GetConfigurationValue("PAYMENT_REMINDER_LESSON_THRESHOLD", "PaymentReminder:LessonThreshold", 3);
            _paymentDeadlineDays = GetConfigurationValue("PAYMENT_REMINDER_DEADLINE_DAYS", "PaymentReminder:PaymentDeadlineDays", 7);
            _period = TimeSpan.FromHours(reminderIntervalHours);

            _logger.LogInformation("PaymentReminderService configured with interval: {Hours} hours, lesson threshold: {Threshold}, payment deadline: {Days} days",
                reminderIntervalHours, _lessonThreshold, _paymentDeadlineDays);
        }

        private int GetConfigurationValue(string environmentVariableName, string configurationKey, int defaultValue)
        {
            // First try environment variable
            var envValue = Environment.GetEnvironmentVariable(environmentVariableName);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                return envIntValue;
            }

            // Then try configuration
            var configValue = _configuration[configurationKey];
            if (!string.IsNullOrEmpty(configValue) && int.TryParse(configValue, out var configIntValue))
            {
                return configIntValue;
            }

            // Return default value
            return defaultValue;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("PaymentReminderService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await SendPaymentRemindersAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during payment reminder processing.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("PaymentReminderService stopped.");
        }

        private async Task SendPaymentRemindersAsync()
        {
            _logger.LogInformation("Starting payment reminder process...");

            using var scope = _serviceProvider.CreateScope();
            var lessonService = scope.ServiceProvider.GetRequiredService<ILessonService>();
            var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();

            try
            {
                var studentsWithLowLessons = await lessonService.GetStudentsWithRemainingLessonsAsync(_lessonThreshold);
                var remindersSent = 0;

                foreach (var student in studentsWithLowLessons)
                {
                    try
                    {
                        var paymentDeadline = DateTime.Now.AddDays(_paymentDeadlineDays).ToString("MMMM dd, yyyy");
                        
                        var placeholders = new Dictionary<string, string>
                        {
                            { "StudentName", student.StudentName },
                            { "LessonsRemaining", student.RemainingLessons.ToString() },
                            { "PaymentDeadline", paymentDeadline }
                        };

                        var success = await emailService.SendEmailFromTemplateAsync("PaymentReminder", student.Email, placeholders);
                        
                        if (success)
                        {
                            remindersSent++;
                            _logger.LogInformation("Payment reminder sent to student {StudentName} ({Email}) with {RemainingLessons} lessons remaining.",
                                student.StudentName, student.Email, student.RemainingLessons);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to send payment reminder to student {StudentName} ({Email}).",
                                student.StudentName, student.Email);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending payment reminder to student {StudentName} ({Email}).",
                            student.StudentName, student.Email);
                    }
                }

                if (remindersSent > 0)
                {
                    _logger.LogInformation("Successfully sent {Count} payment reminder(s).", remindersSent);
                }
                else
                {
                    _logger.LogInformation("No payment reminders needed at this time.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process payment reminders.");
                throw;
            }
        }
    }
}
