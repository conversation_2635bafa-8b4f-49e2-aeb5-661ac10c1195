using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class TimesheetEntry
    {
        public int TimesheetEntryId { get; set; }
        
        [Required]
        public int TimesheetId { get; set; }
        
        [Required]
        public DateTime AttendanceDateTime { get; set; }

        [StringLength(100)]
        public string? Signature { get; set; }
        
        public bool IsPresent { get; set; } = true;
        
        [StringLength(200)]
        public string? Notes { get; set; }
        
        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedUTC { get; set; }

        // Navigation property
        public virtual Timesheet? Timesheet { get; set; }
    }
}
