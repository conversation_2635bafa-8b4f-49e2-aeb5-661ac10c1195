using Microsoft.AspNetCore.Components;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Components
{
    /// <summary>
    /// Base class for all pages that provides common functionality and configuration access.
    /// This class serves as a foundation for pages that need access to application configuration
    /// and shared UI behavior settings.
    /// </summary>
    /// <remarks>
    /// Pages inheriting from this base class automatically get access to:
    /// - Application configuration through the AppConfig property
    /// - ShowActionButtonLabel property for controlling action button text visibility
    ///
    /// The ShowActionButtonLabel configuration can be controlled via:
    /// - Environment variable: SHOW_ACTION_BUTTON_LABEL=true/false
    /// - API configuration: "UI": { "ShowActionButtonLabel": true/false }
    /// - WASM local config: "ShowActionButtonLabel": "true"/"false"
    /// - Default value: true (labels shown)
    /// </remarks>
    public class CommonPageBase : ComponentBase
    {
        /// <summary>
        /// Gets the application configuration injected via dependency injection.
        /// Contains all application-wide settings loaded from the API or local configuration.
        /// </summary>
        [Inject] protected AppConfiguration AppConfig { get; set; } = default!;

        /// <summary>
        /// Gets whether action button labels should be shown based on configuration.
        /// When true, action buttons display text labels alongside icons on desktop screens.
        /// When false, only icons are shown to save space.
        /// </summary>
        /// <returns>
        /// True if action button labels should be displayed, false otherwise.
        /// Defaults to true if configuration is not available.
        /// </returns>
        protected bool ShowActionButtonLabel => AppConfig?.ShowActionButtonLabel ?? false;
    }
}
