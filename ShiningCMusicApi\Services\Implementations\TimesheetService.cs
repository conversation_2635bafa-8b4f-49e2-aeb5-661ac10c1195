using Dapper;
using Microsoft.Data.SqlClient;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class TimesheetService : ITimesheetService
    {
        private readonly string _connectionString;

        public TimesheetService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<Timesheet>> GetTimesheetsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                t.[TimesheetId],
                t.[StudentId],
                t.[TutorId],
                t.[SubjectId],
                --t.[StartDate],
                CAST (t.StartDate AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS StartDate,
                t.[ContactNumber],
                t.[ClassDurationMinutes],
                t.[CreatedUTC],
                t.[UpdatedUTC],
                t.[IsArchived],
                t.[Notes],
                s.<PERSON>, 
                tu.TutorName, 
                sub.Subject as SubjectName
                FROM Timesheets t
                LEFT JOIN Students s ON t.StudentId = s.StudentId
                LEFT JOIN Tutors tu ON t.TutorId = tu.TutorId
                LEFT JOIN Subjects sub ON t.SubjectId = sub.SubjectId
                WHERE t.IsArchived = 0
                ORDER BY t.StartDate DESC, s.StudentName";

            return await connection.QueryAsync<Timesheet>(sql);
        }

        public async Task<IEnumerable<Timesheet>> GetTimesheetsByTutorAsync(int tutorId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                t.[TimesheetId],
                t.[StudentId],
                t.[TutorId],
                t.[SubjectId],
                --t.[StartDate],
                CAST (t.StartDate AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS StartDate,
                t.[ContactNumber],
                t.[ClassDurationMinutes],
                t.[CreatedUTC],
                t.[UpdatedUTC],
                t.[IsArchived],
                t.[Notes],
                s.StudentName, 
                tu.TutorName, 
                sub.Subject as SubjectName
                FROM Timesheets t
                LEFT JOIN Students s ON t.StudentId = s.StudentId
                LEFT JOIN Tutors tu ON t.TutorId = tu.TutorId
                LEFT JOIN Subjects sub ON t.SubjectId = sub.SubjectId
                WHERE t.IsArchived = 0 AND t.TutorId = @TutorId
                ORDER BY t.StartDate DESC, s.StudentName";

            return await connection.QueryAsync<Timesheet>(sql, new { TutorId = tutorId });
        }

        public async Task<Timesheet?> GetTimesheetAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
                t.[TimesheetId],
                t.[StudentId],
                t.[TutorId],
                t.[SubjectId],
                --t.[StartDate],
                CAST (t.StartDate AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS StartDate,
                t.[ContactNumber],
                t.[ClassDurationMinutes],
                t.[CreatedUTC],
                t.[UpdatedUTC],
                t.[IsArchived],
                t.[Notes],
                s.StudentName, 
                tu.TutorName, 
                sub.Subject as SubjectName
                FROM Timesheets t
                LEFT JOIN Students s ON t.StudentId = s.StudentId
                LEFT JOIN Tutors tu ON t.TutorId = tu.TutorId
                LEFT JOIN Subjects sub ON t.SubjectId = sub.SubjectId
                WHERE t.TimesheetId = @Id AND t.IsArchived = 0";

            return await connection.QueryFirstOrDefaultAsync<Timesheet>(sql, new { Id = id });
        }

        public async Task<Timesheet> CreateTimesheetAsync(Timesheet timesheet)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Timesheets (StudentId, TutorId, SubjectId, StartDate, ContactNumber, ClassDurationMinutes, Notes, CreatedUTC, IsArchived)
                VALUES (@StudentId, @TutorId, @SubjectId, @StartDate, @ContactNumber, @ClassDurationMinutes, @Notes, GETUTCDATE(), 0);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                timesheet.StudentId,
                timesheet.TutorId,
                timesheet.SubjectId,
                timesheet.StartDate,
                timesheet.ContactNumber,
                timesheet.ClassDurationMinutes,
                timesheet.Notes
            });

            timesheet.TimesheetId = newId;
            return timesheet;
        }

        public async Task<bool> UpdateTimesheetAsync(int id, Timesheet timesheet)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Timesheets
                SET StudentId = @StudentId,
                    TutorId = @TutorId,
                    SubjectId = @SubjectId,
                    StartDate = @StartDate,
                    ContactNumber = @ContactNumber,
                    ClassDurationMinutes = @ClassDurationMinutes,
                    Notes = @Notes,
                    UpdatedUTC = GETUTCDATE()
                WHERE TimesheetId = @Id AND IsArchived = 0";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                timesheet.StudentId,
                timesheet.TutorId,
                timesheet.SubjectId,
                timesheet.StartDate,
                timesheet.ContactNumber,
                timesheet.ClassDurationMinutes,
                timesheet.Notes
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteTimesheetAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Timesheets
                SET IsArchived = 1, UpdatedUTC = GETUTCDATE()
                WHERE TimesheetId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        // TimesheetEntry operations
        public async Task<IEnumerable<TimesheetEntry>> GetTimesheetEntriesAsync(int timesheetId)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT
	                [TimesheetEntryId],
	                [TimesheetId],
	                --[AttendanceDateTime],
                    CAST (AttendanceDateTime AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS AttendanceDateTime,
                    [Signature],
	                [IsPresent],
	                [Notes],
	                [CreatedUTC],
	                [UpdatedUTC]
                FROM TimesheetEntries
                WHERE TimesheetId = @TimesheetId
                ORDER BY AttendanceDateTime";

            return await connection.QueryAsync<TimesheetEntry>(sql, new { TimesheetId = timesheetId });
        }

        public async Task<TimesheetEntry?> GetTimesheetEntryAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"                
                SELECT
	                [TimesheetEntryId],
	                [TimesheetId],
	                --[AttendanceDateTime],
                    CAST (AttendanceDateTime AT TIME ZONE 'UTC' AT TIME ZONE 'New Zealand Standard Time' AS DATETIME) AS AttendanceDateTime,
	                [Signature],
	                [IsPresent],
	                [Notes],
	                [CreatedUTC],
	                [UpdatedUTC]
                FROM TimesheetEntries
                WHERE TimesheetEntryId = @Id";
            return await connection.QueryFirstOrDefaultAsync<TimesheetEntry>(sql, new { Id = id });
        }

        public async Task<TimesheetEntry> CreateTimesheetEntryAsync(TimesheetEntry entry)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO TimesheetEntries (TimesheetId, AttendanceDateTime, Signature, IsPresent, Notes, CreatedUTC)
                VALUES (@TimesheetId, @AttendanceDateTime, @Signature, @IsPresent, @Notes, GETUTCDATE());

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                entry.TimesheetId,
                entry.AttendanceDateTime,
                entry.Signature,
                entry.IsPresent,
                entry.Notes
            });

            entry.TimesheetEntryId = newId;
            return entry;
        }

        public async Task<bool> UpdateTimesheetEntryAsync(int id, TimesheetEntry entry)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE TimesheetEntries
                SET AttendanceDateTime = @AttendanceDateTime,
                    Signature = @Signature,
                    IsPresent = @IsPresent,
                    Notes = @Notes,
                    UpdatedUTC = GETUTCDATE()
                WHERE TimesheetEntryId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                entry.AttendanceDateTime,
                entry.Signature,
                entry.IsPresent,
                entry.Notes
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteTimesheetEntryAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "DELETE FROM TimesheetEntries WHERE TimesheetEntryId = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }
    }
}
